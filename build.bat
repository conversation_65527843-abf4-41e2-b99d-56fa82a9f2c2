@echo off
echo Building Voice Ballroom VST3 Plugin...

REM Create build directory
if not exist "build" mkdir build
cd build

REM Configure with CMake (assuming JUCE is installed via vcpkg or system-wide)
echo Configuring project with CMake...
cmake .. -G "Visual Studio 17 2022" -A x64

REM Build the project
echo Building project...
cmake --build . --config Release

echo.
echo Build complete! 
echo VST3 plugin should be located in: build\VoiceBallroom_artefacts\Release\VST3\
echo.
echo To install the plugin for FL Studio:
echo 1. Copy the .vst3 file to your VST3 plugins folder
echo 2. Common locations:
echo    - C:\Program Files\Common Files\VST3\
echo    - C:\Users\<USER>\AppData\Roaming\VST3\
echo.
pause
