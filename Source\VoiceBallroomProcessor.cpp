#include "VoiceBallroomProcessor.h"

VoiceBallroomProcessor::VoiceBallroomProcessor()
#ifndef JucePlugin_PreferredChannelConfigurations
    : AudioProcessor(BusesProperties()
#if ! JucePlugin_IsMidiEffect
#if ! JucePlugin_IsSynth
        .withInput("Input", juce::AudioChannelSet::stereo(), true)
#endif
        .withOutput("Output", juce::AudioChannelSet::stereo(), true)
#endif
    )
#endif
{
}

VoiceBallroomProcessor::~VoiceBallroomProcessor()
{
}

const juce::String VoiceBallroomProcessor::getName() const
{
    return JucePlugin_Name;
}

bool VoiceBallroomProcessor::acceptsMidi() const
{
#if JucePlugin_WantsMidiInput
    return true;
#else
    return false;
#endif
}

bool VoiceBallroomProcessor::producesMidi() const
{
#if JucePlugin_ProducesMidiOutput
    return true;
#else
    return false;
#endif
}

bool VoiceBallroomProcessor::isMidiEffect() const
{
#if JucePlugin_IsMidiEffect
    return true;
#else
    return false;
#endif
}

double VoiceBallroomProcessor::getTailLengthSeconds() const
{
    return 3.0; // Reverb tail length
}

int VoiceBallroomProcessor::getNumPrograms()
{
    return 1;
}

int VoiceBallroomProcessor::getCurrentProgram()
{
    return 0;
}

void VoiceBallroomProcessor::setCurrentProgram(int index)
{
}

const juce::String VoiceBallroomProcessor::getProgramName(int index)
{
    return {};
}

void VoiceBallroomProcessor::changeProgramName(int index, const juce::String& newName)
{
}

void VoiceBallroomProcessor::prepareToPlay(double sampleRate, int samplesPerBlock)
{
    // Prepare DSP components
    juce::dsp::ProcessSpec spec;
    spec.sampleRate = sampleRate;
    spec.maximumBlockSize = static_cast<juce::uint32>(samplesPerBlock);
    spec.numChannels = static_cast<juce::uint32>(getTotalNumOutputChannels());

    // Prepare filters
    highPassFilter.prepare(spec);
    presenceBoostFilter.prepare(spec);
    
    // Set up high-pass filter (cut below 100 Hz)
    auto highPassCoeffs = juce::dsp::IIR::Coefficients<float>::makeHighPass(sampleRate, 100.0f, 0.707f);
    highPassFilter.coefficients = highPassCoeffs;
    
    // Set up presence boost filter (around 3-5 kHz)
    auto presenceCoeffs = juce::dsp::IIR::Coefficients<float>::makePeakFilter(sampleRate, 4000.0f, 2.0f, 3.0f);
    presenceBoostFilter.coefficients = presenceCoeffs;

    // Prepare compressor
    compressor.prepare(spec);
    compressor.setThreshold(-20.0f);
    compressor.setRatio(3.0f);
    compressor.setAttack(10.0f);
    compressor.setRelease(100.0f);

    // Prepare reverb with ballroom-style parameters
    reverb.setSampleRate(sampleRate);
    juce::Reverb::Parameters reverbParams;
    reverbParams.roomSize = 0.9f;      // Large room size for ballroom effect
    reverbParams.damping = 0.5f;       // Moderate damping
    reverbParams.wetLevel = 0.4f;      // 40% wet signal
    reverbParams.dryLevel = 0.6f;      // 60% dry signal
    reverbParams.width = 1.0f;         // Full stereo width
    reverbParams.freezeMode = 0.0f;    // No freeze
    reverb.setParameters(reverbParams);
}

void VoiceBallroomProcessor::releaseResources()
{
    // Release any resources when playback stops
}

#ifndef JucePlugin_PreferredChannelConfigurations
bool VoiceBallroomProcessor::isBusesLayoutSupported(const BusesLayout& layouts) const
{
#if JucePlugin_IsMidiEffect
    juce::ignoreUnused(layouts);
    return true;
#else
    if (layouts.getMainOutputChannelSet() != juce::AudioChannelSet::mono()
        && layouts.getMainOutputChannelSet() != juce::AudioChannelSet::stereo())
        return false;

    if (layouts.getMainOutputChannelSet() != layouts.getMainInputChannelSet())
        return false;

    return true;
#endif
}
#endif

void VoiceBallroomProcessor::processBlock(juce::AudioBuffer<float>& buffer, juce::MidiBuffer& midiMessages)
{
    juce::ScopedNoDenormals noDenormals;
    auto totalNumInputChannels = getTotalNumInputChannels();
    auto totalNumOutputChannels = getTotalNumOutputChannels();

    // Clear any output channels that don't contain input data
    for (auto i = totalNumInputChannels; i < totalNumOutputChannels; ++i)
        buffer.clear(i, 0, buffer.getNumSamples());

    // Create audio block for DSP processing
    juce::dsp::AudioBlock<float> block(buffer);
    juce::dsp::ProcessContextReplacing<float> context(block);

    // Apply high-pass filter
    highPassFilter.process(context);
    
    // Apply presence boost
    presenceBoostFilter.process(context);
    
    // Apply compression
    compressor.process(context);

    // Apply reverb (process each channel separately for stereo reverb)
    if (buffer.getNumChannels() == 1)
    {
        reverb.processMono(buffer.getWritePointer(0), buffer.getNumSamples());
    }
    else if (buffer.getNumChannels() >= 2)
    {
        reverb.processStereo(buffer.getWritePointer(0), buffer.getWritePointer(1), buffer.getNumSamples());
    }
}

bool VoiceBallroomProcessor::hasEditor() const
{
    return false; // No GUI for now
}

juce::AudioProcessorEditor* VoiceBallroomProcessor::createEditor()
{
    return nullptr; // No GUI for now
}

void VoiceBallroomProcessor::getStateInformation(juce::MemoryBlock& destData)
{
    // Save plugin state (empty for now)
}

void VoiceBallroomProcessor::setStateInformation(const void* data, int sizeInBytes)
{
    // Restore plugin state (empty for now)
}

// This creates new instances of the plugin
juce::AudioProcessor* JUCE_CALLTYPE createPluginFilter()
{
    return new VoiceBallroomProcessor();
}
