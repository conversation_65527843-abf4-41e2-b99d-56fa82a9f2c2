# 🔨 Build Instructions for Voice Ballroom VST3 Plugin

## Prerequisites

### 1. Install JUCE Framework
- Download JUCE from: https://juce.com/get-juce
- Extract to a folder (e.g., `C:\JUCE`)
- Add JUCE to your system PATH or use vcpkg

### 2. Install Visual Studio 2022
- Download Visual Studio 2022 Community (free)
- Install with C++ development tools
- Ensure CMake tools are included

### 3. Install CMake
- Download from: https://cmake.org/download/
- Add to system PATH during installation

## Building the Plugin

### Option 1: Using the Build Script (Recommended)
1. Open Command Prompt or PowerShell
2. Navigate to the project directory
3. Run: `build.bat`

### Option 2: Manual Build
1. Create build directory: `mkdir build && cd build`
2. Configure: `cmake .. -G "Visual Studio 17 2022" -A x64`
3. Build: `cmake --build . --config Release`

## Installing the Plugin

1. After successful build, locate the VST3 file in:
   `build\VoiceBallroom_artefacts\Release\VST3\VoiceBallroom.vst3`

2. Copy to one of these VST3 directories:
   - **System-wide**: `C:\Program Files\Common Files\VST3\`
   - **User-specific**: `C:\Users\<USER>\AppData\Roaming\VST3\`

3. Restart FL Studio and scan for new plugins

## Plugin Features

✅ **Voice Enhancement**:
- High-pass filter (cuts below 100 Hz)
- Presence boost around 4 kHz
- Compression (threshold: -20 dB, ratio: 3:1)

✅ **Ballroom Reverb**:
- Large room size simulation
- Moderate damping
- 40% wet / 60% dry mix
- Full stereo width

## Troubleshooting

### JUCE Not Found
- Ensure JUCE is properly installed
- Set `JUCE_DIR` environment variable to JUCE installation path
- Or install via vcpkg: `vcpkg install juce`

### Build Errors
- Verify Visual Studio 2022 is installed with C++ tools
- Check that CMake version is 3.22 or higher
- Ensure you're building for x64 architecture

### Plugin Not Loading in FL Studio
- Verify the .vst3 file is in the correct directory
- Check FL Studio's plugin search paths
- Rescan plugins in FL Studio settings
