# 🎛️ Build a JUCE Voice Enhancement Plugin with Ballroom Reverb in Visual Studio

## 🎯 Objective

Create a **VST3 plugin** using **JUCE** in **Visual Studio** that performs:

- **Voice enhancement** using:
  - High-pass EQ (cut below ~100 Hz)
  - Presence boost
  - Compression (threshold -20 dB, ratio 3:1)
- **Ballroom-style reverb** using JUCE's built-in reverb module
- Compatible with **FL Studio** (64-bit, Windows)

---

## 🧱 Project Requirements

- JUCE framework (latest version)
- VST3 SDK (included with JUCE)
- CMake or Projucer-generated build
- Visual Studio 2022+ (x64 build)
- Output format: `.vst3` plugin

---

## 🧰 Plugin Code (Include in Source Files)

```cpp
// VoiceBallroomProcessor.h

#pragma once
#include <JuceHeader.h>

class VoiceBallroomProcessor : public juce::AudioProcessor {
public:
    VoiceBallroomProcessor();
    void prepareToPlay(double sampleRate, int samplesPerBlock) override;
    void processBlock(juce::AudioBuffer<float>&, juce::MidiBuffer&) override;
    const juce::String getName() const override;

    bool acceptsMidi() const override { return false; }
    bool producesMidi() const override { return false; }
    double getTailLengthSeconds() const override { return 3.0; }

    // Other JUCE overrides...

private:
    juce::dsp::IIR::Filter<float> eq;
    juce::dsp::Compressor<float> compressor;
    juce::Reverb reverb;
};

// VoiceBallroomProcessor.cpp

#include \"VoiceBallroomProcessor.h\"

VoiceBallroomProcessor::VoiceBallroomProcessor()
    : juce::AudioProcessor(BusesProperties().withInput(\"Input\", juce::AudioChannelSet::stereo())
                                           .withOutput(\"Output\", juce::AudioChannelSet::stereo())) {}

void VoiceBallroomProcessor::prepareToPlay(double sampleRate, int samplesPerBlock) {
    juce::dsp::ProcessSpec spec { sampleRate, static_cast<juce::uint32>(samplesPerBlock), 2 };
    eq.prepare(spec);
    compressor.prepare(spec);
    reverb.prepare(spec);

    eq.state = *juce::dsp::IIR::Coefficients<float>::makeHighPass(sampleRate, 100.0f);
    compressor.setThreshold(-20.0f);
    compressor.setRatio(3.0f);
    compressor.setAttack(10.0f);
    compressor.setRelease(100.0f);

    juce::Reverb::Parameters params;
    params.roomSize = 0.9f;
    params.damping = 0.5f;
    params.wetLevel = 0.4f;
    params.dryLevel = 0.6f;
    params.width = 1.0f;
    reverb.setParameters(params);
}

void VoiceBallroomProcessor::processBlock(juce::AudioBuffer<float>& buffer, juce::MidiBuffer&) {
    juce::ScopedNoDenormals noDenormals;
    juce::dsp::AudioBlock<float> block(buffer);
    eq.process(juce::dsp::ProcessContextReplacing<float>(block));
    compressor.process(juce::dsp::ProcessContextReplacing<float>(block));

    for (int c = 0; c < buffer.getNumChannels(); ++c)
        reverb.processMono(buffer.getWritePointer(c), buffer.getNumSamples());
}

const juce::String VoiceBallroomProcessor::getName() const {
    return \"VoiceBallroom\";
}

JUCE Configuration
Create project using Projucer or CMake.

Set Plugin Formats:

✅ VST3

❌ AudioUnit (if on Windows only)

Project Settings:

C++17 or higher

Enable JUCE Modules:

juce_audio_processors

juce_dsp

juce_audio_utils

juce_gui_basics (if GUI needed)

